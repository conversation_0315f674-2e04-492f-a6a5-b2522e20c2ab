<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - BitBot | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
</head>
<body class="light-theme">
    <div class="admin-container">
        <!-- Top Header Bar -->
        <header class="top-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle" aria-label="Toggle Sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="brand">
                    <span class="brand-icon">🛡️</span>
                    <span class="brand-text">BitBot Admin</span>
                </div>
            </div>

            <div class="header-right">
                <button class="theme-toggle" id="themeToggle" title="Toggle Dark/Light Mode" aria-label="Toggle Theme">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="user-menu">
                    <button class="user-avatar" id="userMenuToggle" aria-label="User Menu">
                        <i class="fas fa-user-circle"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user"></i>
                            Profile
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item logout-btn" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Right Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <ul class="sidebar-menu">
                    <li class="menu-item">
                        <button class="menu-btn active" data-section="dashboard">
                            <i class="fas fa-chart-pie"></i>
                            <span class="menu-text">Dashboard</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="courses">
                            <i class="fas fa-book"></i>
                            <span class="menu-text">Courses</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="timetables">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="menu-text">Time Tables</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="materials">
                            <i class="fas fa-file-alt"></i>
                            <span class="menu-text">Syllabus</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="notices">
                            <i class="fas fa-bullhorn"></i>
                            <span class="menu-text">Notices</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="fees">
                            <i class="fas fa-dollar-sign"></i>
                            <span class="menu-text">Fee Structure</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="faculty">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span class="menu-text">Faculty</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="college">
                            <i class="fas fa-university"></i>
                            <span class="menu-text">College Info</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="students">
                            <i class="fas fa-users"></i>
                            <span class="menu-text">Students</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="settings">
                            <i class="fas fa-cog"></i>
                            <span class="menu-text">Settings</span>
                        </button>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>

            <!-- Success/Error Messages -->
            <div class="message-container" id="messageContainer"></div>

            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title">Dashboard Overview</h1>
                    <div class="section-actions">
                        <button class="btn btn-outline" id="refreshDashboard">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                        <button class="btn btn-outline" id="exportAllData">
                            <i class="fas fa-download"></i>
                            Export All
                        </button>
                        <button class="btn btn-primary" id="importDataBtn">
                            <i class="fas fa-upload"></i>
                            Import Data
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card" data-aos="fade-up" data-aos-delay="100">
                        <div class="stat-icon courses">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalCourses">0</h3>
                            <p class="stat-label">Total Courses</p>
                            <div class="stat-change positive" id="coursesChange">
                                <i class="fas fa-arrow-up"></i>
                                <span>+0 this month</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="stat-icon students">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalStudents">0</h3>
                            <p class="stat-label">Total Students</p>
                            <div class="stat-change positive" id="studentsChange">
                                <i class="fas fa-arrow-up"></i>
                                <span>+0 this month</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="stat-icon faculty">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalFaculty">0</h3>
                            <p class="stat-label">Faculty Members</p>
                            <div class="stat-change positive" id="facultyChange">
                                <i class="fas fa-arrow-up"></i>
                                <span>+0 this month</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="stat-icon notices">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalNotices">0</h3>
                            <p class="stat-label">Active Notices</p>
                            <div class="stat-change positive" id="noticesChange">
                                <i class="fas fa-arrow-up"></i>
                                <span>+0 this month</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-section" data-aos="fade-up" data-aos-delay="500">
                    <div class="section-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-bolt"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="quick-actions-grid">
                                <button class="action-card" onclick="adminPanel.showSection('courses')">
                                    <div class="action-icon courses">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Add Course</span>
                                        <span class="action-desc">Create new course</span>
                                    </div>
                                </button>

                                <button class="action-card" onclick="adminPanel.showSection('students')">
                                    <div class="action-icon students">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Add Student</span>
                                        <span class="action-desc">Register new student</span>
                                    </div>
                                </button>

                                <button class="action-card" onclick="adminPanel.showSection('notices')">
                                    <div class="action-icon notices">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Post Notice</span>
                                        <span class="action-desc">Create announcement</span>
                                    </div>
                                </button>

                                <button class="action-card" onclick="adminPanel.showSection('timetables')">
                                    <div class="action-icon timetables">
                                        <i class="fas fa-calendar-plus"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Add Timetable</span>
                                        <span class="action-desc">Schedule classes</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- Recent Activity -->
                    <div class="dashboard-section" data-aos="fade-up" data-aos-delay="600">
                        <div class="section-card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-clock"></i>
                                    Recent Activity
                                </h3>
                                <button class="btn btn-sm btn-outline">View All</button>
                            </div>
                            <div class="card-content">
                                <div class="activity-list" id="recentActivity">
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <i class="fas fa-power-off"></i>
                                        </div>
                                        <div class="activity-content">
                                            <p class="activity-text">System initialized successfully</p>
                                            <small class="activity-time">Just now</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Status -->
                    <div class="dashboard-section" data-aos="fade-up" data-aos-delay="700">
                        <div class="section-card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-server"></i>
                                    System Status
                                </h3>
                                <div class="status-badge online">
                                    <i class="fas fa-circle"></i>
                                    All Systems Operational
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="status-grid">
                                    <div class="status-item">
                                        <div class="status-info">
                                            <i class="fas fa-database"></i>
                                            <span>Database Connection</span>
                                        </div>
                                        <div class="status-indicator online">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                    </div>

                                    <div class="status-item">
                                        <div class="status-info">
                                            <i class="fas fa-shield-alt"></i>
                                            <span>Authentication Service</span>
                                        </div>
                                        <div class="status-indicator online">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                    </div>

                                    <div class="status-item">
                                        <div class="status-info">
                                            <i class="fas fa-cloud"></i>
                                            <span>File Storage</span>
                                        </div>
                                        <div class="status-indicator online">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                    </div>

                                    <div class="status-item">
                                        <div class="status-info">
                                            <i class="fas fa-save"></i>
                                            <span>Auto-Save</span>
                                        </div>
                                        <div class="status-indicator" id="autoSaveStatus">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Courses Section -->
            <section id="courses-section" class="content-section">
                <div class="section-header">
                    <h1 class="section-title">Course Management</h1>
                    <div class="section-actions">
                        <button class="btn btn-outline" id="searchCourses">
                            <i class="fas fa-search"></i>
                            Search
                        </button>
                        <button class="btn btn-primary" id="addCourseBtn">
                            <i class="fas fa-plus"></i>
                            Add Course
                        </button>
                    </div>
                </div>

                <div class="content-grid">
                    <div class="form-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-plus-circle"></i>
                                Add New Course
                            </h3>
                        </div>
                        <div class="card-content">
                            <form id="courseForm" class="modern-form">
                                <input type="hidden" id="courseId" name="courseId">

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="courseName">Course Name *</label>
                                        <input type="text" id="courseName" name="courseName" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="duration">Duration *</label>
                                        <input type="text" id="duration" name="duration" placeholder="e.g., 3 Years" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="department">Department *</label>
                                        <input type="text" id="department" name="department" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="totalSeats">Total Seats *</label>
                                        <input type="number" id="totalSeats" name="totalSeats" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="hodName">HOD Name *</label>
                                        <input type="text" id="hodName" name="hodName" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="counsellor">Counsellor</label>
                                        <input type="text" id="counsellor" name="counsellor">
                                    </div>

                                    <div class="form-group full-width">
                                        <label for="scholarshipOpportunities">Scholarship Opportunities</label>
                                        <textarea id="scholarshipOpportunities" name="scholarshipOpportunities" rows="3"></textarea>
                                    </div>

                                    <div class="form-group full-width">
                                        <label for="feeStructure">Fee Structure</label>
                                        <textarea id="feeStructure" name="feeStructure" rows="3"></textarea>
                                    </div>

                                    <div class="form-group full-width">
                                        <label for="admissionEligibility">Admission Eligibility</label>
                                        <textarea id="admissionEligibility" name="admissionEligibility" rows="3"></textarea>
                                    </div>

                                    <div class="form-group full-width">
                                        <label for="courseAffiliation">Course Affiliation</label>
                                        <input type="text" id="courseAffiliation" name="courseAffiliation">
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        Save Course
                                    </button>
                                    <button type="button" class="btn btn-outline" id="resetCourseForm">
                                        <i class="fas fa-undo"></i>
                                        Reset
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="data-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list"></i>
                                Existing Courses
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="data-list" id="coursesList">
                                <!-- Courses will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Time Tables Section -->
            <section id="timetables-section" class="content-section">
                <div class="section-header">
                    <h1 class="section-title">Time Table Management</h1>
                    <div class="section-actions">
                        <button class="btn btn-outline" id="searchTimetables">
                            <i class="fas fa-search"></i>
                            Search
                        </button>
                        <button class="btn btn-primary" id="addTimetableBtn">
                            <i class="fas fa-plus"></i>
                            Add Time Table
                        </button>
                    </div>
                </div>

                <div class="form-container">
                    <form id="timetableForm" class="admin-form">
                        <input type="hidden" id="timetableId" name="timetableId">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="timetableCourse">Course Name *</label>
                                <select id="timetableCourse" name="course" required>
                                    <option value="">Select Course</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="yearSemester">Year/Semester *</label>
                                <input type="text" id="yearSemester" name="yearSemester" placeholder="e.g., 1st Year, 2nd Semester" required>
                            </div>

                            <div class="form-group">
                                <label for="facultyName">Faculty Name *</label>
                                <input type="text" id="facultyName" name="facultyName" required>
                            </div>

                            <div class="form-group">
                                <label for="pdfImageLink">PDF/Image Link</label>
                                <input type="url" id="pdfImageLink" name="pdfImageLink" placeholder="https://...">
                            </div>
                        </div>

                        <div class="timetable-grid">
                            <h3>Weekly Schedule</h3>
                            <div class="days-grid">
                                <div class="day-schedule">
                                    <h4>Monday</h4>
                                    <textarea name="monday" placeholder="Enter Monday schedule"></textarea>
                                </div>
                                <div class="day-schedule">
                                    <h4>Tuesday</h4>
                                    <textarea name="tuesday" placeholder="Enter Tuesday schedule"></textarea>
                                </div>
                                <div class="day-schedule">
                                    <h4>Wednesday</h4>
                                    <textarea name="wednesday" placeholder="Enter Wednesday schedule"></textarea>
                                </div>
                                <div class="day-schedule">
                                    <h4>Thursday</h4>
                                    <textarea name="thursday" placeholder="Enter Thursday schedule"></textarea>
                                </div>
                                <div class="day-schedule">
                                    <h4>Friday</h4>
                                    <textarea name="friday" placeholder="Enter Friday schedule"></textarea>
                                </div>
                                <div class="day-schedule">
                                    <h4>Saturday</h4>
                                    <textarea name="saturday" placeholder="Enter Saturday schedule"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Time Table</button>
                            <button type="button" class="btn btn-secondary" id="resetTimetableForm">Reset</button>
                        </div>
                    </form>
                </div>

                <div class="data-list" id="timetablesList">
                    <!-- Time tables will be loaded here -->
                </div>
            </section>

            <!-- Materials Section -->
            <section id="materials-section" class="admin-section">
                <div class="section-header">
                    <h2>Syllabus & Materials Management</h2>
                    <button class="btn btn-primary" id="addMaterialBtn">+ Add Material</button>
                </div>

                <div class="form-container">
                    <form id="materialForm" class="admin-form">
                        <input type="hidden" id="materialId" name="materialId">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="materialCourse">Course Name *</label>
                                <select id="materialCourse" name="course" required>
                                    <option value="">Select Course</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="materialSemester">Year/Semester *</label>
                                <input type="text" id="materialSemester" name="semester" placeholder="e.g., 1st Year, 2nd Semester" required>
                            </div>

                            <div class="form-group">
                                <label for="subjectName">Subject Name *</label>
                                <input type="text" id="subjectName" name="subjectName" required>
                            </div>

                            <div class="form-group">
                                <label for="fileType">File Type *</label>
                                <select id="fileType" name="fileType" required>
                                    <option value="">Select Type</option>
                                    <option value="Syllabus">Syllabus</option>
                                    <option value="Notes">Notes</option>
                                    <option value="Book">Book</option>
                                </select>
                            </div>

                            <div class="form-group full-width">
                                <label for="refBooks">Reference Books</label>
                                <textarea id="refBooks" name="refBooks" rows="3" placeholder="List reference books"></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="fileUrl">File URL (PDF/DOC) *</label>
                                <input type="url" id="fileUrl" name="fileUrl" placeholder="https://..." required>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Material</button>
                            <button type="button" class="btn btn-secondary" id="resetMaterialForm">Reset</button>
                        </div>
                    </form>
                </div>

                <div class="data-list" id="materialsList">
                    <!-- Materials will be loaded here -->
                </div>
            </section>

            <!-- Notices Section -->
            <section id="notices-section" class="admin-section">
                <div class="section-header">
                    <h2>Notice Management</h2>
                    <button class="btn btn-primary" id="addNoticeBtn">+ Add Notice</button>
                </div>

                <div class="form-container">
                    <form id="noticeForm" class="admin-form">
                        <input type="hidden" id="noticeId" name="noticeId">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="noticeTitle">Title *</label>
                                <input type="text" id="noticeTitle" name="title" required>
                            </div>

                            <div class="form-group">
                                <label for="noticeDate">Date *</label>
                                <input type="date" id="noticeDate" name="date" required>
                            </div>

                            <div class="form-group full-width">
                                <label for="noticeDescription">Description *</label>
                                <textarea id="noticeDescription" name="description" rows="4" required></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="noticeCourses">Courses (Multi-select) *</label>
                                <select id="noticeCourses" name="courses" multiple required>
                                    <!-- Options will be populated dynamically -->
                                </select>
                                <small>Hold Ctrl/Cmd to select multiple courses</small>
                            </div>

                            <div class="form-group full-width">
                                <label for="noticeAttachment">Attachment URL</label>
                                <input type="url" id="noticeAttachment" name="attachment" placeholder="https://...">
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Notice</button>
                            <button type="button" class="btn btn-secondary" id="resetNoticeForm">Reset</button>
                        </div>
                    </form>
                </div>

                <div class="data-list" id="noticesList">
                    <!-- Notices will be loaded here -->
                </div>
            </section>

            <!-- Fee Structure Section -->
            <section id="fees-section" class="admin-section">
                <div class="section-header">
                    <h2>Fee Structure Management</h2>
                    <button class="btn btn-primary" id="addFeeBtn">+ Add Fee Structure</button>
                </div>

                <div class="form-container">
                    <form id="feeForm" class="admin-form">
                        <input type="hidden" id="feeId" name="feeId">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="feeCourse">Course Name *</label>
                                <select id="feeCourse" name="course" required>
                                    <option value="">Select Course</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="admissionFee">Admission Fee *</label>
                                <input type="number" id="admissionFee" name="admissionFee" required>
                            </div>

                            <div class="form-group full-width">
                                <label for="semwiseFee">Semester-wise Fee *</label>
                                <textarea id="semwiseFee" name="semwiseFee" rows="3" placeholder="e.g., Sem 1: ₹50,000, Sem 2: ₹50,000..." required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="hostelFee">Hostel Fee</label>
                                <input type="number" id="hostelFee" name="hostelFee">
                            </div>

                            <div class="form-group">
                                <label for="busFee">Bus Fee</label>
                                <input type="number" id="busFee" name="busFee">
                            </div>

                            <div class="form-group full-width">
                                <label for="scholarshipInfo">Scholarship Information</label>
                                <textarea id="scholarshipInfo" name="scholarshipInfo" rows="3"></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="feePaymentLink">Fee Payment Link</label>
                                <input type="url" id="feePaymentLink" name="feePaymentLink" placeholder="https://...">
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Fee Structure</button>
                            <button type="button" class="btn btn-secondary" id="resetFeeForm">Reset</button>
                        </div>
                    </form>
                </div>

                <div class="data-list" id="feesList">
                    <!-- Fee structures will be loaded here -->
                </div>
            </section>

            <!-- Faculty Section -->
            <section id="faculty-section" class="admin-section">
                <div class="section-header">
                    <h2>Faculty Information Management</h2>
                    <button class="btn btn-primary" id="addFacultyBtn">+ Add Faculty Department</button>
                </div>

                <div class="form-container">
                    <form id="facultyForm" class="admin-form">
                        <input type="hidden" id="facultyId" name="facultyId">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="facultyDepartment">Department Name *</label>
                                <input type="text" id="facultyDepartment" name="department" required>
                            </div>

                            <div class="form-group">
                                <label for="facultyHOD">HOD Name *</label>
                                <input type="text" id="facultyHOD" name="hodName" required>
                            </div>
                        </div>

                        <div class="faculty-list-section">
                            <h3>Faculty Members</h3>
                            <div id="facultyMembersList">
                                <div class="faculty-member">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>Name *</label>
                                            <input type="text" name="facultyName[]" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Subject *</label>
                                            <input type="text" name="facultySubject[]" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Email</label>
                                            <input type="email" name="facultyEmail[]">
                                        </div>
                                        <div class="form-group">
                                            <label>Phone</label>
                                            <input type="tel" name="facultyPhone[]">
                                        </div>
                                        <div class="form-group">
                                            <label>Qualification</label>
                                            <input type="text" name="facultyQualification[]">
                                        </div>
                                        <div class="form-group">
                                            <button type="button" class="btn btn-danger remove-faculty">Remove</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-secondary" id="addFacultyMember">+ Add Faculty Member</button>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Faculty Info</button>
                            <button type="button" class="btn btn-secondary" id="resetFacultyForm">Reset</button>
                        </div>
                    </form>
                </div>

                <div class="data-list" id="facultyList">
                    <!-- Faculty departments will be loaded here -->
                </div>
            </section>

            <!-- College Info Section -->
            <section id="college-section" class="admin-section">
                <div class="section-header">
                    <h2>College Information Management</h2>
                    <button class="btn btn-primary" id="updateCollegeBtn">Update College Info</button>
                </div>

                <div class="form-container">
                    <form id="collegeForm" class="admin-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="collegeName">College Name *</label>
                                <input type="text" id="collegeName" name="collegeName" required>
                            </div>

                            <div class="form-group">
                                <label for="collegeType">College Type *</label>
                                <input type="text" id="collegeType" name="collegeType" placeholder="e.g., Private, Government" required>
                            </div>

                            <div class="form-group">
                                <label for="collegeLocation">Location *</label>
                                <input type="text" id="collegeLocation" name="location" required>
                            </div>

                            <div class="form-group">
                                <label for="establishmentYear">Establishment Year *</label>
                                <input type="number" id="establishmentYear" name="establishmentYear" required>
                            </div>

                            <div class="form-group">
                                <label for="contactEmail">Contact Email *</label>
                                <input type="email" id="contactEmail" name="contactEmail" required>
                            </div>

                            <div class="form-group">
                                <label for="contactPhone">Contact Phone *</label>
                                <input type="tel" id="contactPhone" name="contactPhone" required>
                            </div>

                            <div class="form-group">
                                <label for="affiliatedUniversity">Affiliated University *</label>
                                <input type="text" id="affiliatedUniversity" name="affiliatedUniversity" required>
                            </div>

                            <div class="form-group">
                                <label for="director">Director</label>
                                <input type="text" id="director" name="director">
                            </div>

                            <div class="form-group">
                                <label for="dean">Dean</label>
                                <input type="text" id="dean" name="dean">
                            </div>

                            <div class="form-group">
                                <label for="asstDirector">Assistant Director</label>
                                <input type="text" id="asstDirector" name="asstDirector">
                            </div>

                            <div class="form-group full-width">
                                <label for="facilities">Facilities</label>
                                <textarea id="facilities" name="facilities" rows="3" placeholder="List college facilities"></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="eventsClubs">Events/Clubs</label>
                                <textarea id="eventsClubs" name="eventsClubs" rows="3" placeholder="List events and clubs"></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="googleMapsLocation">Google Maps Location</label>
                                <input type="url" id="googleMapsLocation" name="googleMapsLocation" placeholder="https://maps.google.com/...">
                            </div>

                            <div class="form-group full-width">
                                <label for="hostelInfo">Hostel Information</label>
                                <textarea id="hostelInfo" name="hostelInfo" rows="3" placeholder="Hostel details and facilities"></textarea>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save College Info</button>
                            <button type="button" class="btn btn-secondary" id="resetCollegeForm">Reset</button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Students Section -->
            <section id="students-section" class="admin-section">
                <div class="section-header">
                    <h2>Student Data Management</h2>
                    <button class="btn btn-primary" id="addStudentBtn">+ Add Student</button>
                </div>

                <div class="form-container">
                    <form id="studentForm" class="admin-form">
                        <input type="hidden" id="studentId" name="studentId">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="rollNo">Roll No *</label>
                                <input type="text" id="rollNo" name="rollNo" required>
                            </div>

                            <div class="form-group">
                                <label for="enrollmentNo">Enrollment No *</label>
                                <input type="text" id="enrollmentNo" name="enrollmentNo" required>
                            </div>

                            <div class="form-group">
                                <label for="studentName">Student Name *</label>
                                <input type="text" id="studentName" name="name" required>
                            </div>

                            <div class="form-group">
                                <label for="fatherName">Father's Name</label>
                                <input type="text" id="fatherName" name="fatherName">
                            </div>

                            <div class="form-group">
                                <label for="motherName">Mother's Name</label>
                                <input type="text" id="motherName" name="motherName">
                            </div>

                            <div class="form-group">
                                <label for="studentPhone">Phone *</label>
                                <input type="tel" id="studentPhone" name="phone" required>
                            </div>

                            <div class="form-group">
                                <label for="studentEmail">Email *</label>
                                <input type="email" id="studentEmail" name="email" required>
                            </div>

                            <div class="form-group">
                                <label for="studentCourse">Course *</label>
                                <select id="studentCourse" name="course" required>
                                    <option value="">Select Course</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="studentBranch">Branch</label>
                                <input type="text" id="studentBranch" name="branch">
                            </div>

                            <div class="form-group">
                                <label for="studentYear">Year *</label>
                                <select id="studentYear" name="year" required>
                                    <option value="">Select Year</option>
                                    <option value="1st Year">1st Year</option>
                                    <option value="2nd Year">2nd Year</option>
                                    <option value="3rd Year">3rd Year</option>
                                    <option value="4th Year">4th Year</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="attendance">Attendance %</label>
                                <input type="number" id="attendance" name="attendance" min="0" max="100">
                            </div>

                            <div class="form-group">
                                <label for="feePaid">Fee Paid</label>
                                <input type="number" id="feePaid" name="feePaid">
                            </div>

                            <div class="form-group">
                                <label for="feeDue">Fee Due</label>
                                <input type="number" id="feeDue" name="feeDue">
                            </div>

                            <div class="form-group full-width">
                                <label for="studentAddress">Address</label>
                                <textarea id="studentAddress" name="address" rows="3"></textarea>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Student</button>
                            <button type="button" class="btn btn-secondary" id="resetStudentForm">Reset</button>
                        </div>
                    </form>
                </div>

                <div class="data-list" id="studentsList">
                    <!-- Students will be loaded here -->
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings-section" class="admin-section">
                <div class="section-header">
                    <h2>Settings & Configuration</h2>
                </div>

                <div class="settings-container">
                    <!-- Theme Settings -->
                    <div class="setting-card">
                        <div class="setting-header">
                            <h3>🎨 Appearance</h3>
                            <p>Customize the look and feel of the admin panel</p>
                        </div>
                        <div class="setting-content">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label>Theme Mode</label>
                                    <small>Switch between light and dark themes</small>
                                </div>
                                <div class="setting-control">
                                    <button class="theme-switch" id="themeSwitch">
                                        <span class="switch-slider"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Auto-Save Settings -->
                    <div class="setting-card">
                        <div class="setting-header">
                            <h3>💾 Auto-Save</h3>
                            <p>Automatically save form data as you type</p>
                        </div>
                        <div class="setting-content">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label>Enable Auto-Save</label>
                                    <small>Forms will be saved automatically every 30 seconds</small>
                                </div>
                                <div class="setting-control">
                                    <button class="toggle-switch active" id="autoSaveToggle">
                                        <span class="switch-slider"></span>
                                    </button>
                                </div>
                            </div>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label>Auto-Save Interval</label>
                                    <small>How often to save (in seconds)</small>
                                </div>
                                <div class="setting-control">
                                    <select id="autoSaveInterval">
                                        <option value="15">15 seconds</option>
                                        <option value="30" selected>30 seconds</option>
                                        <option value="60">1 minute</option>
                                        <option value="120">2 minutes</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="setting-card">
                        <div class="setting-header">
                            <h3>🔐 Security</h3>
                            <p>Manage your account security settings</p>
                        </div>
                        <div class="setting-content">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label>Change Password</label>
                                    <small>Update your admin login password</small>
                                </div>
                                <div class="setting-control">
                                    <button class="btn btn-primary" id="changePasswordBtn">Change Password</button>
                                </div>
                            </div>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label>Session Timeout</label>
                                    <small>Automatically logout after inactivity</small>
                                </div>
                                <div class="setting-control">
                                    <select id="sessionTimeout">
                                        <option value="30">30 minutes</option>
                                        <option value="60" selected>1 hour</option>
                                        <option value="120">2 hours</option>
                                        <option value="240">4 hours</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Management -->
                    <div class="setting-card">
                        <div class="setting-header">
                            <h3>📊 Data Management</h3>
                            <p>Import, export, and backup your data</p>
                        </div>
                        <div class="setting-content">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label>Bulk Import</label>
                                    <small>Import data from CSV files</small>
                                </div>
                                <div class="setting-control">
                                    <input type="file" id="csvImportFile" accept=".csv" style="display: none;">
                                    <button class="btn btn-secondary" id="importCsvBtn">📥 Import CSV</button>
                                </div>
                            </div>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label>Export Data</label>
                                    <small>Download all data as CSV files</small>
                                </div>
                                <div class="setting-control">
                                    <button class="btn btn-secondary" id="exportCsvBtn">📤 Export CSV</button>
                                </div>
                            </div>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <label>Backup Database</label>
                                    <small>Create a complete backup of all data</small>
                                </div>
                                <div class="setting-control">
                                    <button class="btn btn-primary" id="backupBtn">💾 Create Backup</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="setting-card">
                        <div class="setting-header">
                            <h3>ℹ️ System Information</h3>
                            <p>View system details and statistics</p>
                        </div>
                        <div class="setting-content">
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Version</label>
                                    <span>BitBot Admin v2.0</span>
                                </div>
                                <div class="info-item">
                                    <label>Last Login</label>
                                    <span id="lastLoginTime">-</span>
                                </div>
                                <div class="info-item">
                                    <label>Database Size</label>
                                    <span id="databaseSize">Calculating...</span>
                                </div>
                                <div class="info-item">
                                    <label>Total Records</label>
                                    <span id="totalRecords">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modal Container -->
    <div id="modalContainer"></div>

    <!-- Password Change Modal -->
    <div class="modal-overlay" id="passwordModal" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3>🔐 Change Password</h3>
                <button class="modal-close" id="closePasswordModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="passwordChangeForm">
                    <div class="form-group">
                        <label for="currentPassword">Current Password *</label>
                        <input type="password" id="currentPassword" name="currentPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">New Password *</label>
                        <input type="password" id="newPassword" name="newPassword" required minlength="6">
                        <small>Password must be at least 6 characters long</small>
                    </div>
                    <div class="form-group">
                        <label for="confirmNewPassword">Confirm New Password *</label>
                        <input type="password" id="confirmNewPassword" name="confirmNewPassword" required>
                    </div>
                    <div class="password-strength" id="passwordStrength"></div>
                </form>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" id="cancelPasswordChange">Cancel</button>
                <button type="button" class="btn btn-primary" id="savePasswordChange">Change Password</button>
            </div>
        </div>
    </div>

    <!-- CSV Import Modal -->
    <div class="modal-overlay" id="csvImportModal" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3>📥 Import CSV Data</h3>
                <button class="modal-close" id="closeCsvImportModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="import-steps">
                    <div class="step active" id="step1">
                        <h4>Step 1: Select Data Type</h4>
                        <select id="importDataType" class="form-control">
                            <option value="">Select data type to import</option>
                            <option value="courses">Courses</option>
                            <option value="students">Students</option>
                            <option value="faculty">Faculty</option>
                            <option value="notices">Notices</option>
                            <option value="materials">Materials</option>
                            <option value="timetables">Timetables</option>
                            <option value="fees">Fee Structures</option>
                        </select>
                    </div>

                    <div class="step" id="step2">
                        <h4>Step 2: Upload CSV File</h4>
                        <div class="file-upload-area" id="fileUploadArea">
                            <input type="file" id="csvFileInput" accept=".csv" style="display: none;">
                            <div class="upload-placeholder">
                                <span class="upload-icon">📁</span>
                                <p>Click to select CSV file or drag and drop</p>
                                <small>Only .csv files are supported</small>
                            </div>
                        </div>
                        <div class="file-info" id="fileInfo" style="display: none;"></div>
                    </div>

                    <div class="step" id="step3">
                        <h4>Step 3: Preview & Import</h4>
                        <div class="preview-container" id="previewContainer">
                            <div class="preview-stats" id="previewStats"></div>
                            <div class="preview-table" id="previewTable"></div>
                        </div>
                    </div>
                </div>

                <div class="import-progress" id="importProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p id="progressText">Importing data...</p>
                </div>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" id="cancelImport">Cancel</button>
                <button type="button" class="btn btn-primary" id="nextStep" disabled>Next</button>
                <button type="button" class="btn btn-primary" id="startImport" style="display: none;">Import Data</button>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal-overlay" id="confirmModal" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 id="confirmTitle">Confirm Action</h3>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">Are you sure you want to perform this action?</p>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" id="confirmCancel">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmOk">Confirm</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="firebase-config.js"></script>
    <script src="firebase-service.js"></script>
    <script src="auth.js"></script>
    <script src="js/admin-panel.js"></script>
    <script>
        // Wait for DOM and Firebase service to be ready
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('Admin panel DOM loaded, checking authentication...');

            // Wait a bit for Firebase service to be fully initialized
            await new Promise(resolve => setTimeout(resolve, 500));

            // Protect this page and initialize logout
            if (typeof protectPage === 'function') {
                console.log('Calling protectPage...');
                await protectPage();
                console.log('protectPage completed');
            } else {
                console.error('protectPage function not found');
            }
        });
    </script>
</body>
</html>
