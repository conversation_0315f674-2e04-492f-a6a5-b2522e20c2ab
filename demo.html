<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Admin Panel Demo - BitBot</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    
    <style>
        /* Demo-specific styles */
        .demo-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
            font-weight: 500;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-container {
            margin-top: 60px;
        }
        
        .demo-features {
            background: var(--bg-primary);
            border: 2px dashed var(--primary-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin: var(--spacing-lg) 0;
            text-align: center;
        }
        
        .demo-features h3 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }
        
        .demo-features ul {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-sm);
        }
        
        .demo-features li {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .demo-features li i {
            color: var(--success-color);
            margin-right: var(--spacing-xs);
        }
    </style>
</head>
<body class="light-theme">
    <!-- Demo Banner -->
    <div class="demo-banner">
        🚀 Modern Admin Panel Demo - Try the sidebar toggle, dark mode, and responsive design!
    </div>

    <div class="admin-container">
        <!-- Top Header Bar -->
        <header class="top-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle" aria-label="Toggle Sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="brand">
                    <span class="brand-icon">🛡️</span>
                    <span class="brand-text">BitBot Admin</span>
                </div>
            </div>
            
            <div class="header-right">
                <button class="theme-toggle" id="themeToggle" title="Toggle Dark/Light Mode" aria-label="Toggle Theme">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="user-menu">
                    <button class="user-avatar" id="userMenuToggle" aria-label="User Menu">
                        <i class="fas fa-user-circle"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user"></i>
                            Profile
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Right Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <ul class="sidebar-menu">
                    <li class="menu-item">
                        <button class="menu-btn active" data-section="dashboard">
                            <i class="fas fa-chart-pie"></i>
                            <span class="menu-text">Dashboard</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="courses">
                            <i class="fas fa-book"></i>
                            <span class="menu-text">Courses</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="timetables">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="menu-text">Time Tables</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="materials">
                            <i class="fas fa-file-alt"></i>
                            <span class="menu-text">Syllabus</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="notices">
                            <i class="fas fa-bullhorn"></i>
                            <span class="menu-text">Notices</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="fees">
                            <i class="fas fa-dollar-sign"></i>
                            <span class="menu-text">Fee Structure</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="faculty">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span class="menu-text">Faculty</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="college">
                            <i class="fas fa-university"></i>
                            <span class="menu-text">College Info</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="students">
                            <i class="fas fa-users"></i>
                            <span class="menu-text">Students</span>
                        </button>
                    </li>
                    <li class="menu-item">
                        <button class="menu-btn" data-section="settings">
                            <i class="fas fa-cog"></i>
                            <span class="menu-text">Settings</span>
                        </button>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Demo Features -->
            <div class="demo-features">
                <h3><i class="fas fa-star"></i> New Features Implemented</h3>
                <ul>
                    <li><i class="fas fa-check"></i> Right-side collapsible sidebar</li>
                    <li><i class="fas fa-check"></i> Modern ChatGPT-like layout</li>
                    <li><i class="fas fa-check"></i> Dark/Light theme toggle</li>
                    <li><i class="fas fa-check"></i> Fully responsive design</li>
                    <li><i class="fas fa-check"></i> Smooth animations & hover effects</li>
                    <li><i class="fas fa-check"></i> Keyboard shortcuts (Ctrl+K, Ctrl+D)</li>
                    <li><i class="fas fa-check"></i> Modern card-based UI</li>
                    <li><i class="fas fa-check"></i> Enhanced user experience</li>
                </ul>
            </div>

            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title">Dashboard Overview</h1>
                    <div class="section-actions">
                        <button class="btn btn-outline">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon courses">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">24</h3>
                            <p class="stat-label">Total Courses</p>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>+3 this month</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon students">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">1,247</h3>
                            <p class="stat-label">Total Students</p>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>+89 this month</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon faculty">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">67</h3>
                            <p class="stat-label">Faculty Members</p>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>+2 this month</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon notices">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">12</h3>
                            <p class="stat-label">Active Notices</p>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>+5 this week</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-section">
                    <div class="section-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-bolt"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="quick-actions-grid">
                                <button class="action-card">
                                    <div class="action-icon courses">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Add Course</span>
                                        <span class="action-desc">Create new course</span>
                                    </div>
                                </button>
                                
                                <button class="action-card">
                                    <div class="action-icon students">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Add Student</span>
                                        <span class="action-desc">Register new student</span>
                                    </div>
                                </button>
                                
                                <button class="action-card">
                                    <div class="action-icon notices">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Post Notice</span>
                                        <span class="action-desc">Create announcement</span>
                                    </div>
                                </button>
                                
                                <button class="action-card">
                                    <div class="action-icon timetables">
                                        <i class="fas fa-calendar-plus"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Add Timetable</span>
                                        <span class="action-desc">Schedule classes</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Other sections would go here -->
            <section id="courses-section" class="content-section">
                <div class="section-header">
                    <h1 class="section-title">Course Management</h1>
                    <div class="section-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Add Course
                        </button>
                    </div>
                </div>
                <div class="section-card">
                    <div class="card-content">
                        <p>Course management interface would be here...</p>
                    </div>
                </div>
            </section>

        </main>
    </div>

    <!-- JavaScript -->
    <script>
        // Simple demo functionality
        class DemoAdminPanel {
            constructor() {
                this.sidebarOpen = false;
                this.darkTheme = false;
                this.isMobile = window.innerWidth <= 1024;
                this.init();
            }

            init() {
                this.setupEventListeners();
                if (!this.isMobile) {
                    this.openSidebar();
                }
            }

            setupEventListeners() {
                // Sidebar toggle
                document.getElementById('sidebarToggle')?.addEventListener('click', () => {
                    this.toggleSidebar();
                });

                // Sidebar overlay
                document.getElementById('sidebarOverlay')?.addEventListener('click', () => {
                    this.closeSidebar();
                });

                // Menu buttons
                document.querySelectorAll('.menu-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const section = e.target.closest('.menu-btn').dataset.section;
                        this.showSection(section);
                        if (this.isMobile) {
                            this.closeSidebar();
                        }
                    });
                });

                // Theme toggle
                document.getElementById('themeToggle')?.addEventListener('click', () => {
                    this.toggleTheme();
                });

                // User menu
                const userMenuToggle = document.getElementById('userMenuToggle');
                const userDropdown = document.getElementById('userDropdown');
                if (userMenuToggle && userDropdown) {
                    userMenuToggle.addEventListener('click', (e) => {
                        e.stopPropagation();
                        userDropdown.classList.toggle('show');
                    });

                    document.addEventListener('click', () => {
                        userDropdown.classList.remove('show');
                    });
                }

                // Window resize
                window.addEventListener('resize', () => {
                    this.handleResize();
                });
            }

            toggleSidebar() {
                if (this.sidebarOpen) {
                    this.closeSidebar();
                } else {
                    this.openSidebar();
                }
            }

            openSidebar() {
                const sidebar = document.getElementById('sidebar');
                const sidebarOverlay = document.getElementById('sidebarOverlay');
                const mainContent = document.getElementById('mainContent');

                if (sidebar) {
                    sidebar.classList.add('open');
                    this.sidebarOpen = true;

                    if (this.isMobile) {
                        sidebarOverlay?.classList.add('show');
                        document.body.style.overflow = 'hidden';
                    } else {
                        mainContent?.classList.add('sidebar-open');
                    }
                }
            }

            closeSidebar() {
                const sidebar = document.getElementById('sidebar');
                const sidebarOverlay = document.getElementById('sidebarOverlay');
                const mainContent = document.getElementById('mainContent');

                if (sidebar) {
                    sidebar.classList.remove('open');
                    this.sidebarOpen = false;

                    if (this.isMobile) {
                        sidebarOverlay?.classList.remove('show');
                        document.body.style.overflow = '';
                    } else {
                        mainContent?.classList.remove('sidebar-open');
                    }
                }
            }

            handleResize() {
                const wasMobile = this.isMobile;
                this.isMobile = window.innerWidth <= 1024;

                if (wasMobile !== this.isMobile) {
                    if (this.isMobile) {
                        this.closeSidebar();
                    } else {
                        this.openSidebar();
                    }
                }
            }

            toggleTheme() {
                this.darkTheme = !this.darkTheme;
                const body = document.body;
                const themeToggle = document.getElementById('themeToggle');
                
                if (this.darkTheme) {
                    body.classList.remove('light-theme');
                    body.classList.add('dark-theme');
                    if (themeToggle) {
                        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                        themeToggle.title = 'Switch to Light Mode';
                    }
                } else {
                    body.classList.remove('dark-theme');
                    body.classList.add('light-theme');
                    if (themeToggle) {
                        themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                        themeToggle.title = 'Switch to Dark Mode';
                    }
                }
            }

            showSection(sectionName) {
                // Hide all sections
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                });

                // Remove active state from all menu buttons
                document.querySelectorAll('.menu-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                // Show target section
                const targetSection = document.getElementById(`${sectionName}-section`);
                if (targetSection) {
                    targetSection.classList.add('active');
                }

                // Set active menu button
                const activeBtn = document.querySelector(`[data-section="${sectionName}"]`);
                if (activeBtn) {
                    activeBtn.classList.add('active');
                }
            }
        }

        // Initialize demo when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new DemoAdminPanel();
        });
    </script>
</body>
</html>
