/* ===== MODERN ADMIN PANEL STYLES ===== */

/* ===== CSS VARIABLES FOR THEMING ===== */
:root {
    /* Light Theme Colors */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-accent: #e2e8f0;

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-tertiary: #94a3b8;
    --text-inverse: #ffffff;

    /* Border Colors */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e0;
    --border-dark: #94a3b8;

    /* Shadow */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;

    /* Sidebar */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 60px;
    --header-height: 70px;
}

/* Dark Theme Variables */
.dark-theme {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #9ca3af;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    --bg-accent: #475569;

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e0;
    --text-tertiary: #94a3b8;
    --text-inverse: #1e293b;

    --border-light: #334155;
    --border-medium: #475569;
    --border-dark: #64748b;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== CONTAINER & LAYOUT ===== */
.admin-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* ===== TOP HEADER ===== */
.top-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.sidebar-toggle {
    background: transparent;
    border: 1px solid var(--border-light);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.sidebar-toggle:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-medium);
    color: var(--text-primary);
}

.brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--text-primary);
}

.brand-icon {
    font-size: 1.5rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* ===== THEME TOGGLE ===== */
.theme-toggle {
    background: transparent;
    border: 1px solid var(--border-light);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.theme-toggle:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-medium);
    color: var(--text-primary);
}

/* ===== USER MENU ===== */
.user-menu {
    position: relative;
}

.user-avatar {
    background: transparent;
    border: 1px solid var(--border-light);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
}

.user-avatar:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-medium);
    color: var(--text-primary);
}

.user-dropdown {
    position: absolute;
    top: calc(100% + var(--spacing-sm));
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: 1001;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color var(--transition-fast);
    border-radius: var(--radius-md);
    margin: var(--spacing-xs);
}

.dropdown-item:hover {
    background: var(--bg-tertiary);
}

.dropdown-item.logout-btn {
    color: var(--danger-color);
}

.dropdown-item.logout-btn:hover {
    background: rgba(239, 68, 68, 0.1);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-light);
    margin: var(--spacing-xs) 0;
}

/* ===== LEFT SIDEBAR ===== */
.sidebar {
    position: fixed;
    top: var(--header-height);
    left: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--header-height));
    background: var(--bg-primary);
    border-right: 1px solid var(--border-light);
    box-shadow: var(--shadow-lg);
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
    z-index: 999;
    overflow-y: auto;
}

.sidebar.open {
    transform: translateX(0);
}

.sidebar-content {
    padding: var(--spacing-lg);
}

.sidebar-menu {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.menu-item {
    width: 100%;
}

.menu-btn {
    width: 100%;
    background: transparent;
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 0.95rem;
    font-weight: 500;
    text-align: left;
}

.menu-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: translateX(-2px);
}

.menu-btn.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
}

.menu-btn i {
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.menu-text {
    flex: 1;
}

/* ===== SIDEBAR OVERLAY ===== */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 998;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* ===== MAIN CONTENT ===== */
.main-content {
    margin-top: var(--header-height);
    margin-left: 0;
    padding: var(--spacing-xl);
    min-height: calc(100vh - var(--header-height));
    transition: margin-left var(--transition-normal);
}

.main-content.sidebar-open {
    margin-left: var(--sidebar-width);
}

/* ===== CONTENT SECTIONS ===== */
.content-section {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
    width: 100%;
    max-width: 100%;
}

.content-section.active {
    display: block;
}

/* Focused content view - hide other sections completely */
.content-section:not(.active) {
    display: none !important;
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
}

.content-section.active {
    display: block !important;
    visibility: visible;
    opacity: 1;
    pointer-events: auto;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section-specific styling */
.content-section .section-header {
    border-bottom: 2px solid var(--primary-color);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
}

.content-section .section-title {
    color: var(--primary-color);
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.content-section .section-title::before {
    content: '';
    width: 4px;
    height: 2rem;
    background: var(--primary-color);
    border-radius: var(--radius-sm);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-light);
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.section-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

/* ===== MODERN BUTTONS ===== */
.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover:before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--primary-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
}

.btn-outline:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-dark);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--text-inverse);
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

/* ===== MODERN CARDS ===== */
.section-card,
.form-card,
.data-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    transition: all var(--transition-fast);
    overflow: hidden;
}

.section-card:hover,
.form-card:hover,
.data-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.card-title i {
    color: var(--primary-color);
}

.card-content {
    padding: var(--spacing-lg);
}

/* ===== DASHBOARD STYLES ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.stat-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-icon.courses {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-icon.students {
    background: linear-gradient(135deg, #10b981, #047857);
}

.stat-icon.faculty {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-icon.notices {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-sm) 0;
    font-weight: 500;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.75rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

.stat-change i {
    font-size: 0.7rem;
}

/* ===== DASHBOARD SECTIONS ===== */
.dashboard-section {
    margin-bottom: var(--spacing-xl);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

/* ===== QUICK ACTIONS ===== */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.action-card {
    background: transparent;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    text-align: left;
}

.action-card:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
}

.action-icon.courses {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.action-icon.students {
    background: linear-gradient(135deg, #10b981, #047857);
}

.action-icon.notices {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.action-icon.timetables {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.action-content {
    flex: 1;
}

.action-title {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.action-desc {
    display: block;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.day-schedule textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    min-height: 80px;
    resize: vertical;
}

/* ===== FACULTY MEMBERS ===== */

.faculty-list-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f7fafc;
    border-radius: 0.5rem;
}

.faculty-list-section h3 {
    margin-bottom: 1rem;
    color: #2d3748;
}

.faculty-member {
    background: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #e2e8f0;
}

.remove-faculty {
    align-self: end;
    margin-top: 1.5rem;
}

/* ===== DATA LISTS ===== */

.data-list {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.data-item {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.data-item:last-child {
    border-bottom: none;
}

.data-info h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.data-info p {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.data-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.data-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

/* ===== LOADING & MESSAGES ===== */

.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 2rem;
    color: #4a5568;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #3182ce;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.message-container {
    margin-bottom: 1rem;
}

.message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.message.success {
    background: #f0fff4;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.message.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #feb2b2;
}

/* ===== MODALS ===== */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
}

.modal-body {
    padding: 1.5rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #718096;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.modal-close:hover {
    background: #f7fafc;
    color: #2d3748;
}

/* ===== PASSWORD MODAL STYLES ===== */

.password-strength {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    display: none;
}

.password-strength.weak {
    background: #fed7d7;
    color: #742a2a;
    display: block;
}

.password-strength.medium {
    background: #fef5e7;
    color: #744210;
    display: block;
}

.password-strength.strong {
    background: #f0fff4;
    color: #22543d;
    display: block;
}

/* ===== CSV IMPORT MODAL STYLES ===== */

.import-steps {
    margin-bottom: 2rem;
}

.step {
    display: none;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.step.active {
    display: block;
    border-color: #3182ce;
    background: #f7fafc;
}

.step h4 {
    margin: 0 0 1rem 0;
    color: #2d3748;
}

.file-upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
}

.file-upload-area:hover {
    border-color: #3182ce;
    background: #f7fafc;
}

.file-upload-area.dragover {
    border-color: #3182ce;
    background: #ebf8ff;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.upload-icon {
    font-size: 3rem;
    opacity: 0.5;
}

.file-info {
    margin-top: 1rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
}

.preview-container {
    max-height: 400px;
    overflow-y: auto;
}

.preview-stats {
    background: #ebf8ff;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #bee3f8;
}

.preview-table {
    overflow-x: auto;
}

.preview-table table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.preview-table th,
.preview-table td {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    text-align: left;
}

.preview-table th {
    background: #f7fafc;
    font-weight: 600;
    color: #2d3748;
}

.import-progress {
    text-align: center;
    padding: 2rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: #3182ce;
    width: 0%;
    transition: width 0.3s ease;
}

/* ===== DARK THEME STYLES ===== */

body.dark-theme {
    background: #1a202c;
    color: #e2e8f0;
}

.dark-theme .admin-nav {
    background: #2d3748;
    border-bottom-color: #4a5568;
}

.dark-theme .nav-brand {
    color: #e2e8f0;
}

.dark-theme .nav-btn {
    background: transparent;
    color: #cbd5e0;
    border-color: #4a5568;
}

.dark-theme .nav-btn:hover {
    background: #4a5568;
    border-color: #718096;
}

.dark-theme .nav-btn.active {
    background: #3182ce;
    color: white;
    border-color: #3182ce;
}

.dark-theme .theme-toggle {
    border-color: #4a5568;
    color: #cbd5e0;
}

.dark-theme .theme-toggle:hover {
    background: #4a5568;
    border-color: #718096;
}

.dark-theme .form-container,
.dark-theme .data-list,
.dark-theme .stat-card,
.dark-theme .action-card,
.dark-theme .activity-list,
.dark-theme .setting-card,
.dark-theme .modal {
    background: #2d3748;
    border-color: #4a5568;
}

.dark-theme .form-group input,
.dark-theme .form-group select,
.dark-theme .form-group textarea {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
}

.dark-theme .form-group input:focus,
.dark-theme .form-group select:focus,
.dark-theme .form-group textarea:focus {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.dark-theme .section-header {
    border-bottom-color: #4a5568;
}

.dark-theme .section-header h2,
.dark-theme .stat-content h3,
.dark-theme .action-text,
.dark-theme .activity-content p,
.dark-theme .setting-header h3,
.dark-theme .setting-info label {
    color: #e2e8f0;
}

.dark-theme .data-item {
    border-bottom-color: #4a5568;
}

.dark-theme .message.success {
    background: #22543d;
    color: #9ae6b4;
    border-color: #38a169;
}

.dark-theme .message.error {
    background: #742a2a;
    color: #feb2b2;
    border-color: #e53e3e;
}

/* ===== DASHBOARD STYLES ===== */

.dashboard-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
}

.stat-content p {
    color: #718096;
    margin: 0.25rem 0;
    font-weight: 500;
}

.stat-change {
    color: #38a169;
    font-size: 0.75rem;
}

.quick-actions {
    margin: 2rem 0;
}

.quick-actions h3 {
    margin-bottom: 1rem;
    color: #2d3748;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.action-card {
    background: white;
    border: 1px solid #e2e8f0;
    padding: 1.5rem;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
}

.action-card:hover {
    border-color: #3182ce;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.15);
}

.action-icon {
    font-size: 2rem;
}

.action-text {
    font-weight: 500;
    color: #2d3748;
}

.recent-activity {
    margin: 2rem 0;
}

.recent-activity h3 {
    margin-bottom: 1rem;
    color: #2d3748;
}

.activity-list {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    font-size: 1.25rem;
    opacity: 0.7;
}

.activity-content p {
    margin: 0;
    color: #2d3748;
    font-weight: 500;
}

.activity-content small {
    color: #718096;
    font-size: 0.75rem;
}

.system-status {
    margin: 2rem 0;
}

.system-status h3 {
    margin-bottom: 1rem;
    color: #2d3748;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    background: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e2e8f0;
}

.status-indicator.online {
    background: #38a169;
    box-shadow: 0 0 0 2px rgba(56, 161, 105, 0.2);
}

.status-indicator.offline {
    background: #e53e3e;
    box-shadow: 0 0 0 2px rgba(229, 62, 62, 0.2);
}

/* ===== SETTINGS STYLES ===== */

.settings-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.setting-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.setting-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.setting-header h3 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
    font-size: 1.25rem;
}

.setting-header p {
    margin: 0;
    color: #718096;
    font-size: 0.875rem;
}

.setting-content {
    padding: 1.5rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f7fafc;
}

.setting-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.setting-info label {
    font-weight: 500;
    color: #2d3748;
    display: block;
    margin-bottom: 0.25rem;
}

.setting-info small {
    color: #718096;
    font-size: 0.75rem;
}

.setting-control {
    flex-shrink: 0;
}

.theme-switch,
.toggle-switch {
    position: relative;
    width: 50px;
    height: 24px;
    background: #e2e8f0;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: background 0.2s;
}

.theme-switch.active,
.toggle-switch.active {
    background: #3182ce;
}

.switch-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.theme-switch.active .switch-slider,
.toggle-switch.active .switch-slider {
    transform: translateX(26px);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f7fafc;
    border-radius: 0.5rem;
}

.info-item label {
    font-weight: 500;
    color: #4a5568;
}

.info-item span {
    color: #2d3748;
    font-weight: 600;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .admin-nav {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .nav-menu {
        justify-content: center;
        width: 100%;
    }
    
    .nav-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
    
    .admin-main {
        padding: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .days-grid {
        grid-template-columns: 1fr;
    }
    
    .data-item {
        flex-direction: column;
        gap: 1rem;
    }
    
    .data-actions {
        align-self: stretch;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .modal-actions {
        flex-direction: column;
    }
}

/* ===== ACTIVITY LIST STYLES ===== */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    transition: background-color var(--transition-fast);
}

.activity-item:hover {
    background: var(--bg-secondary);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    font-size: 0.875rem;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
    font-weight: 500;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

/* ===== FORMS ===== */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.modern-form {
    width: 100%;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group select[multiple] {
    min-height: 120px;
}

.form-group small {
    color: var(--text-tertiary);
    font-size: 0.75rem;
    margin-top: var(--spacing-xs);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-start;
}

/* ===== LOADING INDICATOR ===== */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== MESSAGE CONTAINER ===== */
.message-container {
    margin-bottom: var(--spacing-lg);
}

.message {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.message.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.message.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* ===== TIMETABLE STYLES ===== */
.timetable-grid {
    margin: var(--spacing-xl) 0;
}

.timetable-grid h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.day-schedule {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.day-schedule h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.day-schedule textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    min-height: 80px;
    resize: vertical;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
}

.day-schedule textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== DATA LIST STYLES ===== */
.data-list {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.data-item {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--spacing-lg);
    transition: background-color var(--transition-fast);
}

.data-item:hover {
    background: var(--bg-secondary);
}

.data-item:last-child {
    border-bottom: none;
}

.data-info {
    flex: 1;
}

.data-info h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.data-info p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-xs);
}

.data-info small {
    color: var(--text-tertiary);
    font-size: 0.75rem;
}

.data-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-shrink: 0;
}

.data-actions .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .main-content.sidebar-open {
        margin-left: 0;
    }

    .sidebar {
        width: 100%;
        transform: translateX(-100%);
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .top-header {
        padding: 0 var(--spacing-md);
    }

    .main-content {
        padding: var(--spacing-md);
    }

    .section-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }

    .section-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .action-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin-bottom: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .brand-text {
        display: none;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    .action-card {
        padding: var(--spacing-md);
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }
}
